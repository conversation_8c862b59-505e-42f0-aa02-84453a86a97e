'use client';

import dynamic from 'next/dynamic';
import { match } from 'ts-pattern';
import { Translate } from '@bika/contents/i18n/translate';
import { AttachmentVOExtends } from './interface';
import { PreviewImage } from './preview-image';
import { FileType } from '../file/utils/enum';
import { isWhatFileType, renderFileIconUrl } from '../file/utils/file-type';

export * from './utils';

const PreviewMedia = dynamic(() => import('./preview-media').then((mod) => mod.PreviewMedia), { ssr: false });
const PreviewPdf = dynamic(() => import('./preview-pdf').then((mod) => mod.PreviewPdf), { ssr: false });

interface Props {
  attachment?: AttachmentVOExtends | File;
  t: Translate;
}

/**
 * Attachment 渲染器，被FileNode节点和附件预览的Modal共享使用
 * @param props
 * @returns
 */
export function AttachmentRenderer({ attachment, t }: Props) {
  const fileType = attachment
    ? isWhatFileType({
        name: attachment.name,
        type: 'mimeType' in attachment ? attachment.mimeType : attachment.type,
      })
    : FileType.Image;

  return (
    <div className="w-full h-full">
      {match(fileType)
        .with(FileType.Image, () => <PreviewImage file={attachment as AttachmentVOExtends} />)
        .with(FileType.Media, () => <PreviewMedia file={attachment as AttachmentVOExtends} />)
        .with(FileType.Pdf, () => <PreviewPdf file={attachment as AttachmentVOExtends} t={t} />)
        .otherwise(() => (
          <div className="w-full h-full flex items-center justify-center">
            <img
              src={renderFileIconUrl({
                name: attachment!.name,
                type: 'mimeType' in attachment! ? attachment!.mimeType : attachment!.type,
              })}
              alt=""
            />
          </div>
        ))}
    </div>
  );
}
