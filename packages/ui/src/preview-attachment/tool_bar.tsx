// import { navigationToUrl } from 'pc/components/route_manager/navigation_to_url';
import { useEffect, useState } from 'react';
import type * as React from 'react';
import { Translate } from '@bika/contents/i18n/translate';
import AddCircleOutlined from '@bika/ui/icons/components/add_circle_outlined';
import DownloadOutlined from '@bika/ui/icons/components/download_outlined';
import LinkOutlined from '@bika/ui/icons/components/link_outlined';
import NarrowOutlined from '@bika/ui/icons/components/narrow_outlined';
import RotateOutlined from '@bika/ui/icons/components/rotate_outlined';
import SubtractCircleOutlined from '@bika/ui/icons/components/subtract_circle_outlined';
import { useSnackBar } from '@bika/ui/snackbar/snackbar-component';
import { copyText } from '@bika/ui/utils';
import { MAX_SCALE, MIN_SCALE } from './const';
import type { AttachmentVOExtends, ITransFormInfo } from './interface';
import { Loading } from './loading';
import { ISimpleAttachment } from './preivew-attachment';
import { type IPreviewToolItem, PreviewToolItem } from './tool_item';
import { isImage, isSupportImage } from './utils';
// import { MAX_SCALE, MIN_SCALE } from '../preview_main/constant';
// import { getFile } from '../preview_main/util';
import { directDownload } from './utils/download';

interface IToolBar {
  transformInfo: ITransFormInfo;
  setTransformInfo: React.Dispatch<React.SetStateAction<ITransFormInfo>>;
  fileInfo: AttachmentVOExtends | ISimpleAttachment;

  onClose?: () => void;

  // onDelete(): void;

  onZoom: (scale: number) => void;
  // readonly?: boolean;

  onRotate(): void;

  t: Translate;

  // previewEnable: boolean;
  // isDocType: boolean;
  // officePreviewUrl: string | null;
  // disabledDownload?: boolean;
  // onDownload?: () => void;
}

interface IPreviewToolBar {
  toolLeft?: IPreviewToolItem[];
  title: React.ReactNode;
  toolRight: (IPreviewToolItem | undefined)[];
}

export const MULTIPLE = 1.5;

const copy2clipBoard = async (url: string) => {
  const formatUrl = url.startsWith('http') ? url : `${window.location.origin}${url}`;
  await copyText(formatUrl);
};

export async function download(fileInfo: any) {
  // const href = getDownloadSrc(fileInfo);
  // const href = '';
  // const { name, mimeType: type } = fileInfo;
  // const isImageType = isImage({ name, type });
  // let mode: 'stream' | 'direct' = 'direct';
  // if (isImageType && !isPrivateDeployment()) {
  //   // Message.loading({
  //   //   content: t(Strings.downloading_attachments),
  //   //   duration: 0,
  //   // });
  //   const resp = await DatasheetApi.getContentDisposition(href);
  //   const contentDisposition = resp.data.data;
  //   if (resp.data.code === 500) {
  //     // Message.error({ content: 'SERVER_ERROR' });
  //     return;
  //   }
  //   // If the image contentDisposition type is inline, force the download using a binary stream
  //   if (contentDisposition?.includes('inline')) {
  //     mode = 'stream';
  //   }
  // }
  // if (mode === 'direct') {
  //   directDownload(href, name);
  // } else {
  //   // const blob = await getFile(href);
  //   // FileSaver.saveAs(blob, name);
  // }
  // Message.destroy();
}

export const ToolBar: React.FC<React.PropsWithChildren<IToolBar>> = (props) => {
  const {
    transformInfo,
    fileInfo,
    onClose,
    // onDelete,
    onZoom,
    // readonly,
    onRotate,
    // previewEnable,
    // isDocType,
    // officePreviewUrl,
    // disabledDownload,
    // onDownload,
    t,
  } = props;
  const { toast } = useSnackBar();
  const { scale, initActualScale } = transformInfo;

  const [adaptiveMode, setAdaptiveMode] = useState(true);

  useEffect(() => {
    // initActualScale changes, which means that the image is switched, and the adaptiveMode should be reset.
    setAdaptiveMode(true);
  }, [initActualScale]);

  const areEqualToInitial = Math.abs(1 / initActualScale - scale) < 1e-2;

  const isKkFile = 'variant' in fileInfo && fileInfo.variant === 'kkfile';
  const isImageEditEnabled =
    !isKkFile &&
    isImage({ name: fileInfo.name, type: (fileInfo as unknown as AttachmentVOExtends).mimeType }) &&
    isSupportImage((fileInfo as unknown as AttachmentVOExtends).mimeType);

  const toolLeft = [
    {
      visible: isImageEditEnabled,
      group: [
        {
          component: (
            <span style={{ opacity: scale * initActualScale <= MIN_SCALE ? 0.5 : 1 }}>
              <SubtractCircleOutlined size={16} />
            </span>
          ),
          tip: t.database_fields.attachment.zoom_out,
          onClick: () => onZoom(scale / MULTIPLE),
          style: { marginRight: 0 },
        },
        {
          // Actual scale = Initial scale * scale
          component:
            initActualScale === -1 ? (
              <Loading currentColor />
            ) : (
              `${Math.floor((areEqualToInitial ? 1 : initActualScale * scale) * 100)}%`
            ),
          tip: adaptiveMode ? t.database_fields.attachment.initial_size : t.database_fields.attachment.adaptive,
          onClick: () => {
            if (adaptiveMode) {
              onZoom(1 / initActualScale);
            } else {
              onZoom(1);
            }
            setAdaptiveMode(!adaptiveMode);
          },
          className: 'h-4 w-8 text-center text-xs mx-2 text-white',
        },
        {
          component: (
            <span
              style={{
                opacity: scale * initActualScale >= MAX_SCALE || initActualScale === -1 ? 0.5 : 1,
              }}
            >
              <AddCircleOutlined size={16} />
            </span>
          ),
          tip: t.database_fields.attachment.zoom_in,
          onClick: () => onZoom(scale * MULTIPLE),
        },
        {
          icon: RotateOutlined,
          tip: t.database_fields.attachment.rotate,
          onClick: onRotate,
        },
      ],
      divider: true,
    },
    // {
    //   visible: (isDocType || isPdf({ name: fileInfo.name, type: fileInfo.mimeType })) && previewEnable,
    //   icon: NewtabOutlined,
    //   tip: t(Strings.open_in_new_tab),
    //   onClick: () => officePreviewUrl && navigationToUrl(officePreviewUrl),
    // },
    {
      visible: true,
      component: <LinkOutlined size={16} color="#D9D9D9" />,
      tip: t.database_fields.attachment.copy_link,
      onClick: async () => {
        if ('variant' in fileInfo && fileInfo.variant === 'kkfile') {
          await copy2clipBoard(fileInfo.url);
          toast(t.database_fields.attachment.copy_link_success);
          return;
        }
        await copy2clipBoard((fileInfo as unknown as AttachmentVOExtends).links?.previewUrl ?? '');
        toast(t.database_fields.attachment.copy_link_success);
      },
    },
    {
      visible: true,
      component: <DownloadOutlined size={16} color="#D9D9D9" />,
      tip: t.database_fields.attachment.download,
      onClick: async () => {
        if ('variant' in fileInfo && fileInfo.variant === 'kkfile') {
          await directDownload(fileInfo.url, fileInfo.name);
        } else {
          await directDownload((fileInfo as unknown as AttachmentVOExtends).links?.downloadUrl ?? '', fileInfo.name);
        }
      },
    },
    // {
    //   visible: true,
    //   tip: 'Delete',
    //   icon: DeleteOutlined,
    //   onClick: () => {},
    //   style: { marginRight: 0 },
    // },
  ];
  const toolBarData: IPreviewToolBar = {
    title: fileInfo.name,
    toolRight: [
      ...toolLeft,
      // {
      //   icon: isFullScreen ? NarrowOutlined : ExpandOutlined,
      //   tip: () => t(isFullScreen ? Strings.attachment_preview_exit_fullscreen : Strings.attachment_preview_fullscreen),
      //   onClick: () => toggleIsFullScreen?.(),
      //   className: styles.rightIcon,
      //   visible: !isRecordFullScreen && isSideRecordOpen && !document.querySelector('.centerExpandRecord'),
      // },
      onClose && {
        component: (
          <span>
            <NarrowOutlined color="#D9D9D9" size={16} />
          </span>
        ),
        tip: t.database_fields.attachment.close,
        onClick: onClose,
        className: 'rounded w-6 h-6 hover:bg-[var(--hover)] active:bg-[var(--active)] mr-0',
      },
    ],
  };

  const renderToolItem = (toolItemProps: IPreviewToolItem | undefined, index: number) => {
    if (!toolItemProps) return null;
    const component = toolItemProps.icon ? <toolItemProps.icon size={16} color={''} /> : toolItemProps.component;
    return <PreviewToolItem key={index} {...toolItemProps} component={component} />;
  };

  return (
    <div
      className={
        'flex w-full justify-between flex-shrink-0 p-2 [&>h4]:text-lg [&>h4]:text-center [&>h4]:text-white [&>h4]:m-0 [&>h4]:max-w-[640px] [&>h4]:truncate'
      }
    >
      <div className={'rounded-full w-[280px] flex items-center mr-[15px] select-none'}></div>

      <h4>{toolBarData.title}</h4>

      <div className={'w-[280px] rounded-full flex items-center justify-end'}>
        {toolBarData.toolRight.map(renderToolItem)}
      </div>
    </div>
  );
};
